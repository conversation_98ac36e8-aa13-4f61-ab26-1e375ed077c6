import React, { useState, useEffect } from "react";
import {
  HiChevronUp,
  HiChevronDown,
  HiBookOpen,
  HiAcademicCap,
  HiClock,
  HiDocumentText,
  HiUserGroup,
  HiCheckCircle,
} from "react-icons/hi2";
import CollegeGuideLogo from "../../assets/CollegeGuide.jpg";

const CollegeGuide = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [openAccordions, setOpenAccordions] = useState({});

  // Show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.pageYOffset > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // Toggle accordion
  const toggleAccordion = (id) => {
    setOpenAccordions((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Guide sections data
const guideSections = [
  {
    id: 1,
    title: "هوية الوثيقة ومجالها",
    icon: <HiDocumentText className="w-6 h-6" />,
    content: `...`,
    color: "bg-blue-50 border-blue-200",
  },
  {
    id: 2,
    title: "هيكل اللائحة",
    icon: <HiBookOpen className="w-6 h-6" />,
    content: `...`,
    color: "bg-green-50 border-green-200",
  },
  {
    id: 3,
    title: "الدرجة العلمية ونطاق البرامج",
    icon: <HiAcademicCap className="w-6 h-6" />,
    content: `...`,
    color: "bg-purple-50 border-purple-200",
  },
  {
    id: 4,
    title: "نظام الدراسة",
    icon: <HiClock className="w-6 h-6" />,
    content: `...`,
    color: "bg-yellow-50 border-yellow-200",
  },
  {
    id: 5,
    title: "المواظبة والغياب",
    icon: <HiUserGroup className="w-6 h-6" />,
    content: `...`,
    color: "bg-red-50 border-red-200",
  },
  {
    id: 6,
    title: "التسجيل والإضافة/الحذف والانسحاب",
    icon: <HiClock className="w-6 h-6" />,
    content: `...`,
    color: "bg-orange-50 border-orange-200",
  },
  {
    id: 7,
    title: "نظام الامتحانات والتقويم",
    icon: <HiCheckCircle className="w-6 h-6" />,
    content: `...`,
    color: "bg-indigo-50 border-indigo-200",
  },
  {
    id: 8,
    title: "نظام الاستماع والتعليم الإلكتروني",
    icon: <HiBookOpen className="w-6 h-6" />,
    content: `...`,
    color: "bg-teal-50 border-teal-200",
  },
];


  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50"
      dir="rtl"
    >
      {/* Header Section */}
      <div className="bg-white shadow-lg border-b-4 border-blue-500">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-reverse space-x-4 mb-4 md:mb-0">
              <img
                src={CollegeGuideLogo}
                alt="College Guide Logo"
                className="w-16 h-16 rounded-full shadow-lg border-4 border-blue-200"
              />
              <div>
                <h1 className="text-4xl font-bold text-gray-800 mb-2">
                  دليل الكلية
                </h1>
                <p className="text-lg text-gray-600">
                  كلية الحاسبات والمعلومات - جامعة الزقازيق
                </p>
              </div>
            </div>
            <div className="bg-blue-100 px-6 py-3 rounded-lg">
              <p className="text-blue-800 font-semibold">لائحة 2019</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-blue-500 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  إجمالي الساعات
                </h3>
                <p className="text-3xl font-bold text-blue-600">138</p>
                <p className="text-sm text-gray-600">ساعة معتمدة</p>
              </div>
              < HiAcademicCap className="w-12 h-12 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-green-500 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  المعدل المطلوب
                </h3>
                <p className="text-3xl font-bold text-green-600">2.00</p>
                <p className="text-sm text-gray-600">حد أدنى للتخرج</p>
              </div>
              <HiCheckCircle className="w-12 h-12 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-purple-500 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  نسبة الحضور
                </h3>
                <p className="text-3xl font-bold text-purple-600">75%</p>
                <p className="text-sm text-gray-600">حد أدنى مطلوب</p>
              </div>
              <HiUserGroup className="w-12 h-12 text-purple-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-orange-500 hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  مشروع التخرج
                </h3>
                <p className="text-3xl font-bold text-orange-600">6</p>
                <p className="text-sm text-gray-600">ساعات معتمدة</p>
              </div>
              <HiClock className="w-12 h-12 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Accordion Sections */}
        <div className="space-y-6 mb-12">
          {guideSections.map((section) => (
            <div
              key={section.id}
              className={`bg-white rounded-xl shadow-lg border-2 ${section.color} overflow-hidden`}
            >
              <button
                onClick={() => toggleAccordion(section.id)}
                className="w-full px-6 py-4 text-right flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-reverse space-x-3">
                  <div className="text-blue-600">{section.icon}</div>
                  <h3 className="text-xl font-bold text-gray-800">
                    {section.title}
                  </h3>
                </div>
                {openAccordions[section.id] ? (
                  <HiChevronUp className="w-6 h-6 text-gray-600" />
                ) : (
                  <HiChevronDown className="w-6 h-6 text-gray-600" />
                )}
              </button>
              {openAccordions[section.id] && (
                <div className="px-6 pb-6">
                  <div className="border-t pt-4">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {section.content}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Academic Levels Table */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <HiAcademicCap className="w-8 h-8 text-blue-600 ml-3" />
            الانتقال بين المستويات الأكاديمية
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-blue-50">
                  <th className="border border-blue-200 px-4 py-3 text-right font-bold text-blue-800">
                    المستوى
                  </th>
                  <th className="border border-blue-200 px-4 py-3 text-right font-bold text-blue-800">
                    الساعات المطلوبة
                  </th>
                  <th className="border border-blue-200 px-4 py-3 text-right font-bold text-blue-800">
                    الوصف
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-4 py-3 font-semibold">
                    المستوى الأول
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    0 - 28 ساعة
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    يبقى في المستوى الأول حتى يجتاز 28 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-4 py-3 font-semibold">
                    المستوى الثاني
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    28 - 60 ساعة
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    ينتقل للثاني بعد اجتياز 28 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-4 py-3 font-semibold">
                    المستوى الثالث
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    60 - 100 ساعة
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    ينتقل للثالث بعد اجتياز 60 ساعة معتمدة
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="border border-gray-200 px-4 py-3 font-semibold">
                    المستوى الرابع
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    100+ ساعة
                  </td>
                  <td className="border border-gray-200 px-4 py-3">
                    ينتقل للرابع بعد اجتياز 100 ساعة معتمدة
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Requirements Breakdown */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <HiCheckCircle className="w-8 h-8 text-green-600 ml-3" />
            متطلبات الحصول على درجة البكالوريوس
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 rounded-lg p-6 border-2 border-blue-200">
              <h3 className="text-xl font-bold text-blue-800 mb-4">
                متطلبات عامة
              </h3>
              <div className="space-y-2">
                <p className="text-blue-700">
                  <span className="font-bold">12 ساعة</span> إجمالي
                </p>
                <p className="text-blue-600">6 ساعات إجبارية</p>
                <p className="text-blue-600">6 ساعات اختيارية</p>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-6 border-2 border-green-200">
              <h3 className="text-xl font-bold text-green-800 mb-4">
                متطلبات الكلية
              </h3>
              <div className="space-y-2">
                <p className="text-green-700">
                  <span className="font-bold">66 ساعة</span> إجمالي
                </p>
                <p className="text-green-600">24 ساعة علوم أساسية</p>
                <p className="text-green-600">42 ساعة علوم حاسب</p>
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-6 border-2 border-purple-200">
              <h3 className="text-xl font-bold text-purple-800 mb-4">
                متطلبات التخصص
              </h3>
              <div className="space-y-2">
                <p className="text-purple-700">
                  <span className="font-bold">60 ساعة</span> إجمالي
                </p>
                <p className="text-purple-600">42 ساعة إجبارية</p>
                <p className="text-purple-600">12 ساعة اختيارية</p>
                <p className="text-purple-600">6 ساعات مشروع التخرج</p>
              </div>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl shadow-lg p-6 mb-12 border-2 border-red-200">
          <h2 className="text-2xl font-bold text-red-800 mb-6 flex items-center">
            <HiDocumentText className="w-8 h-8 text-red-600 ml-3" />
            ملحوظات مهمة للطالب
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border-r-4 border-red-500">
                <h4 className="font-bold text-red-800 mb-2">الحضور والغياب</h4>
                <p className="text-gray-700">
                  الالتزام بالحضور أساسي للسماح بدخول الامتحان النهائي (حد أدنى
                  75%). تجاوز 25% غياب بلا عذر يؤدي للحرمان و"راسب".
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border-r-4 border-blue-500">
                <h4 className="font-bold text-blue-800 mb-2">
                  التقدم بين المستويات
                </h4>
                <p className="text-gray-700">
                  مرتبط بعدد الساعات المجتازة (28/60/100)، فخطّط لتوزيع مقرراتك
                  بما يضمن عدم تعطّل انتقالك.
                </p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border-r-4 border-green-500">
                <h4 className="font-bold text-green-800 mb-2">
                  متطلبات التخرج
                </h4>
                <p className="text-gray-700">
                  138 ساعة ومعدل تراكمي ≥ 2.00، مع إتمام التدريب والمشروع. لا
                  يُحتسب مقرر "حقوق الإنسان ومكافحة الفساد" في المعدل التراكمي.
                </p>
              </div>
              <div className="bg-white rounded-lg p-4 border-r-4 border-purple-500">
                <h4 className="font-bold text-purple-800 mb-2">
                  التدريب والمشروع
                </h4>
                <p className="text-gray-700">
                  لهما وزن وتأثير فعلي في تأهيلك المهني والعلمي. التزم بالتقرير،
                  وتفاعل مع المشرفين.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Training and Project Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-blue-500">
            <h3 className="text-xl font-bold text-blue-800 mb-4 flex items-center">
              <HiUserGroup className="w-6 h-6 ml-2" />
              التدريب العملي والميداني
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التوقيت:</span> خلال عطلة
                  صيفية بعد اجتياز 60 ساعة معتمدة
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">الإشراف:</span> تحت إشراف
                  أعضاء هيئة التدريس
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التقرير:</span> إعداد تقرير عن
                  فترة التدريب وتسليمه
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">شرط للتخرج:</span> نجاح الطالب
                  في التدريب شرط للحصول على البكالوريوس
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-green-500">
            <h3 className="text-xl font-bold text-green-800 mb-4 flex items-center">
              <HiClock className="w-6 h-6 ml-2" />
              مشروع التخرج
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">البداية:</span> عادة في
                  المستوى الرابع
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">المدة:</span> فترة مكثفة تصل
                  إلى أربع أسابيع
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">التقييم:</span> تقرير علمي
                  ومناقشة أمام لجنة
                </p>
              </div>
              <div className="flex items-start space-x-reverse space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <p className="text-gray-700">
                  <span className="font-semibold">القيمة:</span> 6 ساعات معتمدة
                  على فصلين دراسيين
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <HiBookOpen className="w-8 h-8 text-indigo-600 ml-3" />
            معلومات إضافية مهمة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-bold text-indigo-800 mb-3">
                نظام الامتحانات والتقويم
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>توجد مواد مستقلة لنظام الامتحانات والتقويم</span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>
                    الطالب الذي يرسب في المقرر أكثر من مرة يُحتسب الرسوب مرة
                    واحدة فقط في المعدل
                  </span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>تبقى كل مرات الرسوب مُثبتة في السجل الأكاديمي</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-bold text-indigo-800 mb-3">
                بيان الدرجات والخدمات
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start space-x-reverse space-x-2">
                  <HiCheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>يحق للخريج أو المنسحب الحصول على بيان درجات</span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>
                    يجوز منح الطالب الوافد بيان درجات لضرورات التأشيرة
                  </span>
                </li>
                <li className="flex items-start space-x-reverse space-x-2">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5" />
                  <span>لا يُمنح البيان مع وجود رسوم دراسية غير مسدّدة</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Summary */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-8 text-white">
          <h2 className="text-2xl font-bold mb-4 text-center">
            خلاصة تنفيذية مركّزة
          </h2>
          <p className="text-lg leading-relaxed text-center">
            اللائحة تنظّم رحلة الطالب منذ القبول وحتى التخرج عبر إطار ساعات
            معتمدة واضح، وضوابط حضور وتقييم دقيقة، وتدرج مرحلي بين المستويات
            مرتبط بالساعات المجتازة، مع متطلبات تخرج محددة (138 ساعة، GPA ≥
            2.00)، وركيزتين تطبيقيتين أساسيتين: التدريب العملي/الميداني ومشروع
            التخرج.
          </p>
        </div>
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 left-8 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-50"
          aria-label="العودة إلى الأعلى"
        >
          <HiChevronUp className="w-6 h-6" />
        </button>
      )}
    </div>
  );
};

export default CollegeGuide;
